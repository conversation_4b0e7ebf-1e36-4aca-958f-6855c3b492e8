<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 页面标题 -->
    <view class="bg-white px-4 py-3 border-b border-gray-100">
      <text class="text-lg font-semibold text-gray-800">月度奖励</text>
    </view>

    <!-- 统计信息 -->
    <view class="bg-white mx-4 mt-4 rounded-lg shadow-sm p-4">
      <view class="flex items-center justify-between">
        <view class="text-center">
          <text class="text-2xl font-bold text-mint">{{ totalAmount.toFixed(2) }}</text>
          <text class="block text-sm text-gray-500 mt-1">总奖励金额</text>
        </view>
        <view class="w-px h-8 bg-gray-200"></view>
        <view class="text-center">
          <text class="text-2xl font-bold text-mint">{{ pendingCount }}</text>
          <text class="block text-sm text-gray-500 mt-1">待入账</text>
        </view>
        <view class="w-px h-8 bg-gray-200"></view>
        <view class="text-center">
          <text class="text-2xl font-bold text-green-500">{{ completedCount }}</text>
          <text class="block text-sm text-gray-500 mt-1">已入账</text>
        </view>
      </view>
    </view>

    <!-- 月度奖励列表 -->
    <view class="mx-4 mt-4">
      <!-- 空状态 -->
      <view v-if="list.length === 0" class="bg-white rounded-lg shadow-sm p-8 text-center">
        <view class="text-gray-400 text-6xl mb-4">📅</view>
        <text class="text-gray-500 text-base">暂无月度奖励记录</text>
        <text class="block text-gray-400 text-sm mt-2">达成月度目标后将获得奖励</text>
      </view>

      <!-- 月度奖励列表项 -->
      <view v-else class="space-y-3">
        <view
          v-for="(item, index) in list"
          :key="index"
          class="bg-white rounded-lg shadow-sm p-4 border border-gray-100 hover:shadow-md transition-shadow"
        >
          <view class="flex items-center justify-between mb-3">
            <!-- 左侧月份信息 -->
            <view class="flex items-center flex-1">
              <!-- 月份图标 -->
              <view class="w-12 h-12 bg-mint-light rounded-full flex items-center justify-center mr-3">
                <text class="text-mint text-lg font-semibold">{{ getMonthNumber(item.yearMonth) }}</text>
              </view>

              <!-- 月份详情 -->
              <view class="flex-1">
                <view class="flex items-center mb-1">
                  <text class="text-base font-medium text-gray-800 mr-2">{{ formatYearMonth(item.yearMonth) }}</text>
                  <view :class="getStatusClass(item.status)">
                    <text class="text-xs">{{ getStatusText(item.status) }}</text>
                  </view>
                </view>
                <view class="flex items-center text-sm text-gray-500">
                  <text>生成时间: {{ formatDate(item.createTime) }}</text>
                </view>
              </view>
            </view>

            <!-- 右侧金额 -->
            <view class="text-right">
              <text class="text-lg font-bold text-mint">¥{{ item.amount.toFixed(2) }}</text>
            </view>
          </view>

          <!-- 底部时间线 -->
          <view class="flex items-center pt-2 border-t border-gray-100">
            <view class="w-2 h-2 rounded-full mr-2" :class="getStatusDotClass(item.status)"></view>
            <text class="text-xs text-gray-400">{{ getStatusDescription(item.status) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部提示 -->
    <view v-if="list.length > 0" class="text-center py-6">
      <text class="text-gray-400 text-sm">已显示全部 {{ list.length }} 个月度奖励</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, computed} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getPartnerMonthlyCommissionListData} from "@/common/api/partner"

interface Item{
  yearMonth: string; // 年月 yyyy-MM
  amount: number;
  status: number; // 状态:1=待入账,2=已入账,3=已拒绝
  createTime: string;
}

const list = ref<Item[]>([])

// iOS兼容的日期解析函数
const parseDate = (dateStr: string): Date => {
  if (!dateStr) return new Date()
  const isoString = dateStr.replace(/\s/, 'T')
  const date = new Date(isoString)

  if (isNaN(date.getTime())) {
    const fallbackString = dateStr.replace(/-/g, '/').replace(/T/, ' ')
    return new Date(fallbackString)
  }

  return date
}

// 计算总奖励金额
const totalAmount = computed(() => {
  return list.value.reduce((sum, item) => sum + item.amount, 0)
})

// 计算待入账数量
const pendingCount = computed(() => {
  return list.value.filter(item => item.status === 1).length
})

// 计算已入账数量
const completedCount = computed(() => {
  return list.value.filter(item => item.status === 2).length
})

// 格式化年月显示
const formatYearMonth = (yearMonth: string): string => {
  if (!yearMonth) return ''
  const [year, month] = yearMonth.split('-')
  return `${year}年${parseInt(month)}月`
}

// 获取月份数字（用于图标显示）
const getMonthNumber = (yearMonth: string): string => {
  if (!yearMonth) return '0'
  const [, month] = yearMonth.split('-')
  return parseInt(month).toString()
}

// 格式化日期显示
const formatDate = (dateStr: string): string => {
  const date = parseDate(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 获取状态文本
const getStatusText = (status: number): string => {
  switch (status) {
    case 1:
      return '待入账'
    case 2:
      return '已入账'
    case 3:
      return '已拒绝'
    default:
      return '未知'
  }
}

// 获取状态描述
const getStatusDescription = (status: number): string => {
  switch (status) {
    case 1:
      return '奖励待处理，请耐心等待'
    case 2:
      return '奖励已成功入账'
    case 3:
      return '奖励申请被拒绝'
    default:
      return '状态未知'
  }
}

// 获取状态样式类
const getStatusClass = (status: number): string => {
  switch (status) {
    case 1:
      return 'bg-yellow-100 text-yellow-600 px-2 py-0.5 rounded-full'
    case 2:
      return 'bg-green-100 text-green-600 px-2 py-0.5 rounded-full'
    case 3:
      return 'bg-red-100 text-red-600 px-2 py-0.5 rounded-full'
    default:
      return 'bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full'
  }
}

// 获取状态指示点样式类
const getStatusDotClass = (status: number): string => {
  switch (status) {
    case 1:
      return 'bg-yellow-400'
    case 2:
      return 'bg-green-400'
    case 3:
      return 'bg-red-400'
    default:
      return 'bg-gray-400'
  }
}

const init = async () => {
  try {
    list.value = await getPartnerMonthlyCommissionListData()
  } catch (error) {
    console.error('获取月度奖励列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}

onShow(() => {
  init()
})
</script>


<style scoped lang="scss">

</style>
