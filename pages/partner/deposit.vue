<template>
  <view>

  </view>
</template>

<script setup lang="ts">
import {ref, computed} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getPartnerDepositLogListData} from "@/common/api/partner"
interface Item{
  beforeAmount: number; // 变动前金额
  changeAmount: number; // 变动金额
  afterAmount: number; // 变动后金额
  type: number; // 类型:1=主动缴纳,2=提成抵扣,3=退还
  createTime: string; // 发生时间
}
const list = ref<Item[]>([])
const init = async () => {
  try {
    list.value = await getPartnerDepositLogListData()
  } catch (error) {
    console.error('获取押金记录列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}

onShow(() => {
  init()
})


</script>


<style scoped lang="scss">

</style>
